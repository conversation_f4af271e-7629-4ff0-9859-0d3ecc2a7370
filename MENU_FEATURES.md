# 🎉 菜单功能增强说明

## ✨ 新增功能

### 1. 🎯 手动选择去重策略

现在在所有去重操作中，您可以手动选择最适合的去重策略：

```
╔══════════════════════════════════════════════════════╗
║                                                      ║
║                🎯 选择去重策略                        ║
║                                                      ║
╠══════════════════════════════════════════════════════╣
║                                                      ║
║  1️⃣  FULL - 完全匹配 (最高精度)                      ║
║  2️⃣  SMART_PRIORITY - 智能优先级匹配                 ║
║  3️⃣  NORMALIZED - 标准化精准匹配                     ║
║  4️⃣  PRECISE - 精准匹配 (平衡精度)                   ║
║  5️⃣  SERVER_PORT_TYPE - 基础匹配                     ║
║  6️⃣  NAME - 基于节点名称                             ║
║  7️⃣  智能去重 (自动选择最佳策略)                     ║
║                                                      ║
╚══════════════════════════════════════════════════════╝
```

**策略说明：**
- **FULL**: 最高精度，匹配所有配置字段
- **SMART_PRIORITY**: 智能优先级，根据协议自动选择重要字段
- **NORMALIZED**: 标准化后精准匹配，处理字段名差异
- **PRECISE**: 平衡精度和实用性
- **SERVER_PORT_TYPE**: 基础匹配，仅比较服务器、端口、协议
- **NAME**: 基于节点名称去重
- **智能去重**: 自动尝试多种策略，选择最佳结果

### 2. 📄 手动选择输出格式

处理文件时可以选择生成的输出格式：

```
╔══════════════════════════════════════════════════════╗
║                                                      ║
║                📄 选择输出格式                        ║
║                                                      ║
╠══════════════════════════════════════════════════════╣
║                                                      ║
║  1️⃣  Clash YAML (推荐)                               ║
║  2️⃣  Base64 订阅                                     ║
║  3️⃣  URL 列表                                        ║
║  4️⃣  JSON 数据                                       ║
║  5️⃣  全部格式 (生成所有格式)                         ║
║                                                      ║
╚══════════════════════════════════════════════════════╝
```

**格式说明：**
- **Clash YAML**: 适用于 Clash 客户端的配置文件
- **Base64 订阅**: 标准的 Base64 编码订阅链接
- **URL 列表**: 原始代理 URL 列表
- **JSON 数据**: 结构化的 JSON 格式数据
- **全部格式**: 同时生成所有支持的格式

### 3. 🎨 精美的菜单界面

全新设计的菜单界面，使用 Unicode 字符绘制边框：

```
╔════════════════════════════════════════════════════════════════════╗
║                                                                    ║
║            🚀 代理节点处理工具 - 交互式菜单                        ║
║                                                                    ║
╠════════════════════════════════════════════════════════════════════╣
║                                                                    ║
║                        📋 功能菜单                                 ║
║                                                                    ║
║  1️⃣  处理所有文件 (自动转换格式)                                   ║
║  2️⃣  合并YAML文件 (去重后生成单个YAML文件)                        ║
║  3️⃣  合并Base64文件 (去重后生成单个Base64文件)                     ║
║  4️⃣  合并URL文件 (去重后生成单个URL文件)                          ║
║  5️⃣  智能合并所有文件 (按格式分类合并)                            ║
║  6️⃣  查看文件统计信息                                              ║
║                                                                    ║
║  0️⃣  退出程序                                                      ║
║                                                                    ║
╚════════════════════════════════════════════════════════════════════╝
```

## 🚀 使用方法

### 启动菜单
```bash
npm run menu
```

### 功能流程

#### 1. 处理所有文件
1. 选择去重策略
2. 选择输出格式
3. 自动处理所有文件

#### 2. 合并文件
1. 选择要合并的文件类型
2. 选择去重策略
3. 确认合并操作

#### 3. 智能合并
1. 自动扫描所有文件
2. 选择去重策略
3. 按格式分类合并

## 🎯 优势

### 1. **灵活性**
- 可根据需求选择最适合的去重策略
- 支持多种输出格式
- 支持自定义配置

### 2. **用户体验**
- 精美的界面设计
- 清晰的操作流程
- 详细的进度提示

### 3. **功能完整**
- 支持所有主流代理协议
- 多种去重策略可选
- 完整的统计信息

## 📊 默认配置

- **默认去重策略**: FULL (完全匹配)
- **默认输出格式**: 根据功能而定
- **默认重命名**: 启用，按地区分组

## 🔧 技术实现

- 模块化设计，易于扩展
- 配置参数传递机制
- 错误处理和用户友好提示
- 支持自定义配置覆盖

---

**享受全新的菜单体验！** 🎉
