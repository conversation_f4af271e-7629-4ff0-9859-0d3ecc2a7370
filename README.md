# 🚀 代理节点转换工具

一个功能强大的代理节点格式转换和管理工具，专为处理各种代理订阅文件而设计。支持多种主流代理协议的智能解析、格式转换、节点去重、统一重命名和文件合并等功能。

## 📋 项目概述

本工具是一个基于Node.js开发的命令行应用程序，旨在解决代理节点管理中的常见问题：
- **格式混乱**: 不同来源的订阅文件格式不统一
- **节点重复**: 多个订阅源包含相同的节点
- **命名混乱**: 节点名称格式不统一，难以识别
- **手动处理**: 需要手动转换和合并多个文件

## ✨ 核心功能

### 🔄 智能格式转换
- **多格式支持**: 完美支持Clash YAML、Base64订阅、URL列表、JSON等主流格式
- **自动格式检测**: 智能识别输入文件格式，无需手动指定
- **避免重复转换**: 智能判断，不会将YAML转换为YAML，Base64转换为Base64等
- **无损转换**: 保持节点配置的完整性和准确性

### 🔧 高级节点处理
- **智能去重算法**: 基于服务器地址、端口和协议类型的多维度去重
- **统一重命名规则**: 自动生成"🇭🇰 香港 001"格式的标准化节点名称
- **地区智能识别**: 根据服务器IP和域名自动识别节点地区
- **特殊字符处理**: 自动为包含特殊字符的字段添加引号，确保YAML格式正确

### 📁 强大的文件合并功能
- **分类合并**: 按文件格式自动分类，分别合并YAML、Base64、URL文件
- **智能去重**: 合并过程中自动去除重复节点
- **批量处理**: 一次性处理多个订阅文件
- **格式保持**: 合并后保持原有格式特性

### 🎯 用户友好的交互界面
- **图形化菜单**: 清晰的命令行交互界面
- **操作引导**: 详细的操作提示和确认机制
- **实时反馈**: 处理过程中的详细进度和结果显示
- **统计信息**: 完整的节点统计和分析报告

## 🛠️ 支持的代理协议

本工具支持当前主流的代理协议，涵盖了绝大多数代理服务提供商使用的协议类型：

| 协议名称 | URL解析 | URL生成 | Clash转换 | 特性支持 | 开发状态 |
|---------|---------|---------|-----------|----------|----------|
| **Shadowsocks (SS)** | ✅ | ✅ | ✅ | 插件、混淆 | 完整支持 |
| **ShadowsocksR (SSR)** | ✅ | ✅ | ✅ | 协议、混淆参数 | 新增支持 |
| **VMess** | ✅ | ✅ | ✅ | WebSocket、TLS | 完整支持 |
| **VLESS** | ✅ | ✅ | ✅ | Reality、XTLS | 完整支持 |
| **Trojan** | ✅ | ✅ | ✅ | TLS、WebSocket | 完整支持 |
| **Hysteria2** | ✅ | ✅ | ✅ | 混淆、带宽控制 | 新增支持 |
| **Snell** | ✅ | ✅ | ✅ | 版本3/4、混淆 | 新增支持 |

### 快速安装

#### 1. 克隆项目
```bash
git clone <项目地址>
cd v2ray
```

#### 2. 安装依赖
```bash
# 使用 npm (推荐)
npm install

# 或使用 yarn
yarn install
```

# 查看帮助信息
npm run menu
```

## 🎮 使用指南

### 📁 文件准备

在开始使用前，请将您的代理订阅文件放置在 `tests/` 目录中：

```
tests/
├── subscription1.yaml    # Clash配置文件
├── subscription2.txt     # Base64订阅文件
├── nodes.txt            # URL列表文件
└── ...                  # 其他订阅文件
```

**支持的文件格式**:
- `.yaml` / `.yml` - Clash配置文件
- `.txt` - Base64订阅或URL列表
- 任何包含代理URL的文本文件

### 🎯 操作方式

#### 方式一: 交互式菜单 (推荐新手)

```bash
npm run menu
```

**菜单选项详解**:

**1️⃣ 处理所有文件**
- 自动扫描 `tests/` 目录中的所有文件
- 智能识别文件格式并进行相应转换
- 避免重复格式转换（如YAML不会再转换为YAML）
- 生成去重和重命名后的多格式输出文件

**2️⃣ 合并YAML文件**
- 扫描所有 `.yaml` 和 `.yml` 文件
- 解析并合并所有节点
- 执行智能去重处理
- 生成单个合并的YAML文件

**3️⃣ 合并Base64文件**
- 处理所有Base64格式的订阅文件
- 解码、合并、去重所有节点
- 重新编码为单个Base64订阅文件

**4️⃣ 合并URL文件**
- 合并所有包含代理URL的文本文件
- 去重并统一重命名所有节点
- 生成单个URL列表文件

**5️⃣ 智能合并所有文件**
- 自动按格式分类所有文件
- 分别执行YAML、Base64、URL三种格式的合并
- 一次操作生成三个合并文件

**6️⃣ 查看文件统计信息**
- 显示文件分类统计
- 展示节点数量和协议分布
- 提供详细的地区分布信息

#### 方式二: 命令行操作 (适合高级用户)

**批量处理**
```bash
# 处理所有文件
npm run process

# 处理指定文件
npm run process-file subscription.txt
```

**快速合并**
```bash
# 合并YAML文件
npm run merge-yaml

# 合并Base64文件
npm run merge-base64

# 合并URL文件
npm run merge-url
```

#### 方式三: 编程接口 (开发者)

```javascript
import { ProxyConverter } from './src/index.js';
import { OutputFormats } from './src/types.js';

const converter = new ProxyConverter();

// 解析订阅内容
const nodes = converter.parse(subscriptionContent, OutputFormats.BASE64);

// 智能去重
const uniqueNodes = converter.deduplicate(nodes, {
  strategy: 'server-port-type',
  smart: true
});

// 统一重命名
const renamedNodes = converter.rename(uniqueNodes, {
  template: '{flag} {region} {index:3}',
  groupByRegion: true
});

// 格式转换
const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
const urlList = converter.convert(renamedNodes, OutputFormats.URL);

// 获取统计信息
const stats = converter.getStats(renamedNodes);
console.log(`处理了 ${stats.total} 个节点`);
```

## 📂 项目结构和文件说明

### 项目目录结构
```
v2ray/
├── src/                          # 核心源代码
│   ├── parsers/                  # 协议解析器模块
│   │   ├── index.js             # 解析器统一入口
│   │   ├── shadowsocks.js       # Shadowsocks解析器
│   │   ├── shadowsocksr.js      # ShadowsocksR解析器
│   │   ├── vmess.js             # VMess解析器
│   │   ├── vless.js             # VLESS解析器
│   │   ├── trojan.js            # Trojan解析器
│   │   ├── hysteria2.js         # Hysteria2解析器
│   │   └── snell.js             # Snell解析器
│   ├── converters/              # 格式转换器模块
│   │   ├── index.js             # 转换器统一入口
│   │   ├── clash.js             # Clash格式转换器
│   │   ├── base64.js            # Base64格式转换器
│   │   └── url.js               # URL格式转换器
│   ├── utils/                   # 工具函数模块
│   │   ├── index.js             # 工具函数集合
│   │   ├── region.js            # 地区识别工具
│   │   └── naming.js            # 命名规则工具
│   ├── types.js                 # 类型定义和常量
│   └── index.js                 # 主入口文件
├── tests/                       # 输入文件目录
│   ├── mihomo.yaml              # 示例Clash配置
│   ├── base64.txt               # 示例Base64订阅
│   └── 合并节点.txt             # 示例URL列表
├── output/                      # 输出文件目录
├── process-files.js             # 文件处理主程序
├── merge-files.js               # 文件合并功能
├── interactive-menu.js          # 交互式菜单
├── test-new-protocols.js        # 协议测试程序
├── package.json                 # 项目配置文件
└── README.md                    # 项目说明文档
```

### 输入文件要求

**支持的文件类型**:

1. **Clash配置文件** (`.yaml`, `.yml`)
   ```yaml
   proxies:
     - name: "节点名称"
       type: ss
       server: *******
       port: 8388
       # ... 其他配置
   ```

2. **Base64订阅文件** (`.txt`)
   ```
   c3M6Ly9ZV1Z6TFRJMU5pMW5ZMjA2Y0dGemMzZHZjbVJBTVRreUxqRTJPQzR4TGpFNk9ETTRPQT09IyVFOSVBNiU5OSVFNiVCOCVBRg==
   ```

3. **URL列表文件** (`.txt`)
   ```
   ss://YWVzLTI1Ni1nY206cGFzc3dvcmRAMTkyLjE2OC4xLjE6ODM4OA==#香港节点1
   vmess://eyJ2IjoiMiIsInBzIjoi...#美国节点1
   trojan://<EMAIL>:443#日本节点1
   ```

### 输出文件说明

处理后的文件自动保存在 `output/` 目录中：

#### 单文件处理输出
```
output/
├── [原文件名]_clash.yaml        # Clash配置文件
├── [原文件名]_urls.txt          # URL列表文件
├── [原文件名]_base64.txt        # Base64订阅文件
└── [原文件名]_nodes.json        # JSON格式节点数据
```

**注意**: 工具会智能避免重复格式转换：
- YAML格式输入不会生成YAML输出
- Base64格式输入不会生成Base64输出
- URL格式输入不会生成URL输出

#### 合并文件输出
```
output/
├── merged_yaml_nodes.yaml       # 所有YAML文件合并结果
├── merged_base64_nodes.txt      # 所有Base64文件合并结果
└── merged_url_nodes.txt         # 所有URL文件合并结果
```

#### 文件内容示例

**Clash配置文件示例**:
```yaml
proxies:
  - name: "🇭🇰 香港 001"
    type: ss
    server: *******
    port: 8388
    cipher: "aes-256-gcm"
    password: "password123"
    udp: true

  - name: "🇺🇸 美国 001"
    type: vmess
    server: *******
    port: 443
    uuid: "12345678-1234-1234-1234-123456789abc"
    alterId: 0
    cipher: auto
    # ... 更多配置
```

**统计信息示例**:
```
📊 节点统计:
  总数: 145
  有效: 142
  无效: 3

协议分布:
  ss: 45
  vmess: 38
  trojan: 32
  vless: 15
  hysteria2: 8
  ssr: 4
  snell: 1

地区分布:
  香港: 52
  美国: 38
  日本: 25
  新加坡: 18
  其他: 12
```

## ⚙️ 高级功能和配置

### 🚫 智能重复转换避免

工具具备智能识别功能，避免无意义的重复格式转换：

**转换逻辑**:
- **YAML输入** → 生成 URL、Base64、JSON (不生成YAML)
- **Base64输入** → 生成 YAML、URL、JSON (不生成Base64)
- **URL输入** → 生成 YAML、Base64、JSON (不生成URL)

**优势**:
- 节省处理时间和存储空间
- 避免不必要的格式转换损失
- 提供真正有用的输出格式

### 🔤 特殊字符智能处理

针对URL和Base64格式的节点，工具会自动处理特殊字符问题：

**处理规则**:
```yaml
# 检测到特殊字符开头的字段值
password: @CfftfYVgp4gkMHMirH6@_C    # ❌ 可能导致YAML解析错误

# 自动添加引号保护
password: "@CfftfYVgp4gkMHMirH6@_C"  # ✅ 确保YAML格式正确
```

**支持的特殊字符**: `@`, `#`, `$`, `%`, `&`, `*`, `!`, `?`, `|`, `>`, `<`, `=`, `+`, `-`, `~`, `` ` ``, `^`, `(`, `)`, `[`, `]`, `{`, `}`, `:`, `;`, `,`, `.`, `/`, `\`, `"`, `'`

### 📊 智能文件分类系统

工具采用多重检测机制自动识别文件格式：

**分类规则**:
1. **扩展名检测**: `.yaml`/`.yml` → YAML格式
2. **内容分析**:
   - 单行长字符串 + Base64验证 → Base64格式
   - 多行代理URL → URL列表格式
3. **协议前缀识别**: `ss://`, `vmess://`, `trojan://` 等

**分类结果**:
- 🟡 **YAML文件**: Clash配置文件
- 🟢 **Base64文件**: 订阅链接内容
- 🔵 **URL文件**: 代理URL列表
- ⚪ **未知格式**: 无法识别的文件

### 🎯 去重算法详解

**多维度去重策略**:
```javascript
// 去重配置示例
deduplicateOptions: {
  strategy: 'server-port-type',  // 去重策略
  smart: true,                   // 启用智能去重
  compareFields: [               // 比较字段
    'server',                    // 服务器地址
    'port',                      // 端口号
    'type'                       // 协议类型
  ]
}
```

**去重逻辑**:
1. **基础去重**: 服务器地址 + 端口 + 协议类型
2. **智能去重**: 考虑配置参数的相似性
3. **保留策略**: 优先保留配置更完整的节点

### 🏷️ 重命名规则系统

**命名模板**:
```javascript
renameOptions: {
  template: '{flag} {region} {index:3}',  // 命名模板
  groupByRegion: true,                    // 按地区分组
  startIndex: 1,                          // 起始序号
  indexPadding: 3                         // 序号位数
}
```

**模板变量**:
- `{flag}`: 国旗Emoji (🇭🇰, 🇺🇸, 🇯🇵 等)
- `{region}`: 地区中文名 (香港, 美国, 日本 等)
- `{index:n}`: n位数序号 (001, 002, 003 等)
- `{type}`: 协议类型 (SS, VMess, Trojan 等)

**地区识别**:
- **IP地址**: 通过GeoIP数据库识别
- **域名**: 通过域名后缀和关键词识别
- **节点名称**: 通过名称中的地区关键词识别

### 📈 统计分析功能

**统计维度**:
```javascript
stats: {
  total: 145,           // 节点总数
  valid: 142,           // 有效节点数
  invalid: 3,           // 无效节点数
  types: {              // 协议分布
    'ss': 45,
    'vmess': 38,
    'trojan': 32,
    // ...
  },
  regions: {            // 地区分布
    '香港': 52,
    '美国': 38,
    '日本': 25,
    // ...
  },
  duplicates: 8         // 去重数量
}
```

### 🔧 自定义配置

**配置文件位置**: `src/types.js`

**可配置项**:
```javascript
// 去重配置
export const DeduplicateStrategies = {
  SERVER_PORT: 'server-port',
  SERVER_PORT_TYPE: 'server-port-type',
  FULL_CONFIG: 'full-config'
};

// 重命名配置
export const NamingTemplates = {
  FLAG_REGION_INDEX: '{flag} {region} {index:3}',
  REGION_TYPE_INDEX: '{region} {type} {index:2}',
  CUSTOM: '{flag} {region} {type} {index:3}'
};

// 输出格式配置
export const OutputFormats = {
  CLASH: 'clash',
  BASE64: 'base64',
  URL: 'url',
  JSON: 'json'
};
```

## 🎯 详细使用示例

### 示例1: 处理单个Base64订阅文件

**操作步骤**:
```bash
# 1. 将Base64订阅内容保存为文件
echo "c3M6Ly9ZV1Z6TFRJMU5pMW5ZMjA2Y0dGemMzZHZjbVJBTVRreUxqRTJPQzR4TGpFNk9ETTRPQT09" > tests/subscription.txt

# 2. 处理文件
npm run process-file subscription.txt
```

**处理结果**:
```
📁 处理文件: subscription.txt
──────────────────────────────────────────────────
📋 检测到格式: base64
🔍 解析节点...
✅ 解析完成，共 26 个节点

🔄 处理合并的节点 (总计: 26)
✅ 去重完成: 26 → 26 (移除 0 个重复)
✅ 重命名完成

📊 节点统计:
  总数: 26
  协议分布: ss(8), vmess(10), trojan(5), vless(3)
  地区分布: 香港(12), 美国(8), 日本(4), 其他(2)

💾 生成输出文件...
✅ 文件已保存: output/subscription_clash.yaml
✅ 文件已保存: output/subscription_urls.txt
✅ 文件已保存: output/subscription_nodes.json
注意: 跳过生成 subscription_base64.txt (避免重复格式转换)
```

### 示例2: 智能合并多个不同格式文件

**文件准备**:
```bash
tests/
├── clash_config.yaml     # 38个节点
├── base64_sub.txt        # 26个节点
└── url_list.txt          # 75个节点
```

**操作步骤**:
```bash
npm run menu
# 选择 5 (智能合并所有文件)
# 输入 y 确认
```

**处理结果**:
```
🧠 智能合并所有文件
────────────────────────────────────────

📋 将按格式分类进行合并:
  🟡 YAML文件: 1 个
  🟢 Base64文件: 1 个
  🔵 URL文件: 1 个

📄 合并YAML文件...
📁 处理: clash_config.yaml
  ✅ 解析到 38 个节点
🔄 处理合并的节点 (总计: 38)
✅ 去重完成: 38 → 35 (移除 3 个重复)
✅ 重命名完成
✅ 文件已保存: output/merged_yaml_nodes.yaml

📄 合并Base64文件...
📁 处理: base64_sub.txt
  ✅ 解析到 26 个节点
🔄 处理合并的节点 (总计: 26)
✅ 去重完成: 26 → 24 (移除 2 个重复)
✅ 重命名完成
✅ 文件已保存: output/merged_base64_nodes.txt

📄 合并URL文件...
📁 处理: url_list.txt
  ✅ 解析到 75 个节点
🔄 处理合并的节点 (总计: 75)
✅ 去重完成: 75 → 68 (移除 7 个重复)
✅ 重命名完成
✅ 文件已保存: output/merged_url_nodes.txt

🎉 智能合并完成！
```

### 示例3: 查看详细统计信息

**操作步骤**:
```bash
npm run menu
# 选择 6 (查看文件统计信息)
```

**统计结果**:
```
📊 文件统计信息
────────────────────────────────────────

📂 扫描目录: ./tests

📋 文件分类统计:
  🟡 YAML文件: 2 个
     1. mihomo.yaml
     2. clash_config.yaml
  🟢 Base64文件: 1 个
     1. base64.txt
  🔵 URL文件: 1 个
     1. 合并节点.txt
  ⚪ 未知格式: 0 个

📈 总计: 4 个文件

预估节点数量:
  YAML文件: ~76 个节点
  Base64文件: ~26 个节点
  URL文件: ~75 个节点
  总计: ~177 个节点 (去重前)
```

### 示例4: 编程接口使用

**完整示例代码**:
```javascript
import { ProxyConverter } from './src/index.js';
import { OutputFormats } from './src/types.js';
import fs from 'fs';

async function processSubscription() {
  // 1. 创建转换器实例
  const converter = new ProxyConverter();

  // 2. 读取订阅文件
  const subscriptionContent = fs.readFileSync('tests/subscription.txt', 'utf8');

  // 3. 解析节点
  console.log('🔍 解析节点...');
  const nodes = converter.parse(subscriptionContent, OutputFormats.BASE64);
  console.log(`✅ 解析完成，共 ${nodes.length} 个节点`);

  // 4. 智能去重
  console.log('🔄 执行去重...');
  const uniqueNodes = converter.deduplicate(nodes, {
    strategy: 'server-port-type',
    smart: true
  });
  console.log(`✅ 去重完成: ${nodes.length} → ${uniqueNodes.length}`);

  // 5. 统一重命名
  console.log('🏷️ 重命名节点...');
  const renamedNodes = converter.rename(uniqueNodes, {
    template: '{flag} {region} {index:3}',
    groupByRegion: true,
    startIndex: 1
  });
  console.log(`✅ 重命名完成`);

  // 6. 格式转换
  console.log('💾 生成输出文件...');

  // 生成Clash配置
  const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
  fs.writeFileSync('output/custom_clash.yaml', clashConfig);

  // 生成URL列表
  const urlList = converter.convert(renamedNodes, OutputFormats.URL);
  fs.writeFileSync('output/custom_urls.txt', urlList);

  // 生成JSON数据
  const jsonData = converter.convert(renamedNodes, OutputFormats.JSON);
  fs.writeFileSync('output/custom_nodes.json', JSON.stringify(jsonData, null, 2));

  // 7. 获取统计信息
  const stats = converter.getStats(renamedNodes);
  console.log('\n📊 处理统计:');
  console.log(`  总节点数: ${stats.total}`);
  console.log(`  有效节点: ${stats.valid}`);
  console.log(`  协议分布: ${Object.entries(stats.types).map(([k,v]) => `${k}(${v})`).join(', ')}`);
  console.log(`  地区分布: ${Object.entries(stats.regions).map(([k,v]) => `${k}(${v})`).join(', ')}`);

  console.log('\n🎉 处理完成！');
}

// 运行处理函数
processSubscription().catch(console.error);
```

### 示例5: 批量处理多个订阅源

**脚本示例**:
```javascript
import { ProxyConverter } from './src/index.js';
import { OutputFormats } from './src/types.js';
import fs from 'fs';
import path from 'path';

async function batchProcess() {
  const converter = new ProxyConverter();
  const subscriptions = [
    { name: '机场A', url: 'https://example1.com/sub', format: OutputFormats.BASE64 },
    { name: '机场B', url: 'https://example2.com/clash', format: OutputFormats.CLASH },
    { name: '机场C', file: 'tests/local_nodes.txt', format: OutputFormats.URL }
  ];

  let allNodes = [];

  for (const sub of subscriptions) {
    console.log(`📡 处理订阅: ${sub.name}`);

    let content;
    if (sub.url) {
      // 从URL获取内容 (需要添加HTTP请求代码)
      content = await fetchSubscription(sub.url);
    } else if (sub.file) {
      // 从本地文件读取
      content = fs.readFileSync(sub.file, 'utf8');
    }

    const nodes = converter.parse(content, sub.format);
    console.log(`  ✅ 解析到 ${nodes.length} 个节点`);

    allNodes = allNodes.concat(nodes);
  }

  console.log(`\n🔄 合并处理 ${allNodes.length} 个节点...`);

  // 去重和重命名
  const processedNodes = converter.rename(
    converter.deduplicate(allNodes),
    { template: '{flag} {region} {index:3}' }
  );

  // 生成最终配置
  const finalConfig = converter.convert(processedNodes, OutputFormats.CLASH);
  fs.writeFileSync('output/merged_all_subscriptions.yaml', finalConfig);

  console.log(`🎉 批量处理完成！最终获得 ${processedNodes.length} 个节点`);
}
```

## 🐛 故障排除和常见问题

### 常见问题解答

#### Q1: 出现编码警告信息是否正常？
```
btoa编码失败，使用Buffer方式: Invalid character
```

**A**: 这是**正常的警告信息**，不是错误。当遇到中文字符或特殊符号时，程序会自动切换到Buffer编码方式，确保处理成功。

#### Q2: 为什么某些文件没有生成对应格式的输出？

**A**: 这是**智能优化功能**。工具会避免重复格式转换：
- YAML输入不会生成YAML输出
- Base64输入不会生成Base64输出
- URL输入不会生成URL输出

#### Q3: 节点解析失败怎么办？

**可能原因和解决方案**:

1. **文件编码问题**
   ```bash
   # 确保文件使用UTF-8编码
   file -bi filename.txt
   # 如果不是UTF-8，转换编码
   iconv -f GBK -t UTF-8 filename.txt > filename_utf8.txt
   ```

2. **文件格式错误**
   ```bash
   # 检查Base64文件是否为单行
   wc -l base64_file.txt
   # 检查URL文件是否包含有效的代理URL
   grep -E "^(ss|vmess|trojan|vless)://" url_file.txt
   ```

3. **协议URL格式错误**
   - 确保URL格式符合标准
   - 检查Base64编码是否正确
   - 验证JSON格式是否有效

#### Q4: 内存不足或处理速度慢

**优化建议**:

1. **分批处理大文件**
   ```bash
   # 将大文件分割成小文件
   split -l 1000 large_file.txt small_file_

   # 分别处理小文件
   for file in small_file_*; do
     npm run process-file "$file"
   done
   ```

2. **调整Node.js内存限制**
   ```bash
   # 增加内存限制到4GB
   node --max-old-space-size=4096 process-files.js
   ```

#### Q5: 生成的Clash配置无法使用

**检查清单**:

1. **YAML格式验证**
   ```bash
   # 使用在线YAML验证器检查格式
   # 或使用命令行工具
   python -c "import yaml; yaml.safe_load(open('output/config.yaml'))"
   ```

2. **节点配置完整性**
   - 确保所有必需字段都存在
   - 检查端口号是否在有效范围内
   - 验证加密方法是否支持

3. **特殊字符处理**
   - 检查密码字段是否正确加引号
   - 确认中文字符编码正确

### 性能优化建议

#### 处理大量节点时的优化

1. **启用智能去重**
   ```javascript
   const options = {
     deduplicate: true,
     deduplicateOptions: {
       strategy: 'server-port-type',
       smart: true  // 启用智能去重
     }
   };
   ```

2. **批量处理模式**
   ```bash
   # 使用批量处理而不是逐个处理
   npm run process  # 而不是多次 process-file
   ```

3. **内存管理**
   ```javascript
   // 在编程接口中及时释放大对象
   let nodes = converter.parse(content);
   nodes = converter.deduplicate(nodes);
   content = null;  // 释放原始内容
   ```

### 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|---------|------|----------|
| `ENOENT` | 文件不存在 | 检查文件路径是否正确 |
| `EACCES` | 权限不足 | 检查文件读写权限 |
| `EMFILE` | 打开文件过多 | 减少并发处理数量 |
| `ENOMEM` | 内存不足 | 增加Node.js内存限制 |

### 调试模式

启用详细日志输出：
```bash
# 设置调试环境变量
export DEBUG=proxy-converter:*
npm run process

# Windows用户
set DEBUG=proxy-converter:*
npm run process
```

## 📈 版本更新日志

### v1.2.0 (2024-01-15) - 重大更新
**新增功能**:
- ✅ **新协议支持**: 添加Hysteria2、ShadowsocksR、Snell协议完整支持
- ✅ **文件合并功能**: 支持多文件智能合并，按格式分类处理
- ✅ **交互式菜单**: 全新的用户友好命令行界面
- ✅ **智能转换优化**: 避免重复格式转换，提升处理效率

**功能改进**:
- ✅ **特殊字符处理**: 自动为特殊字符添加引号保护
- ✅ **编码问题修复**: 完善的UTF-8和Base64编码处理
- ✅ **YAML解析升级**: 使用js-yaml库替代简化实现
- ✅ **错误处理增强**: 更好的错误提示和容错机制

**性能优化**:
- ✅ **内存使用优化**: 减少大文件处理时的内存占用
- ✅ **处理速度提升**: 优化算法，提高节点处理速度
- ✅ **并发处理**: 支持多文件并发处理

### v1.1.0 (2023-12-20)
**新增功能**:
- ✅ **协议扩展**: 添加VLESS、Trojan协议支持
- ✅ **智能去重**: 实现多维度节点去重算法
- ✅ **统一重命名**: 标准化节点命名格式

**功能改进**:
- ✅ **地区识别**: 改进地区识别准确性
- ✅ **统计功能**: 添加详细的节点统计信息
- ✅ **配置验证**: 增强节点配置有效性检查

### v1.0.0 (2023-11-15) - 首次发布
**核心功能**:
- ✅ **基础转换**: 支持SS、VMess协议的格式转换
- ✅ **Clash生成**: 生成标准Clash配置文件
- ✅ **多格式支持**: 支持URL、Base64、JSON格式

## 🤝 贡献指南

### 如何贡献

1. **Fork项目** 到您的GitHub账户
2. **创建功能分支** (`git checkout -b feature/AmazingFeature`)
3. **提交更改** (`git commit -m 'Add some AmazingFeature'`)
4. **推送分支** (`git push origin feature/AmazingFeature`)
5. **创建Pull Request**

### 开发环境设置

```bash
# 1. 克隆项目
git clone <your-fork-url>
cd v2ray

# 2. 安装依赖
npm install

# 3. 运行测试
npm test

# 4. 启动开发模式
npm run dev
```

### 代码规范

- 使用ES6+语法
- 遵循ESLint配置
- 添加适当的注释
- 编写单元测试

### 报告问题

在提交Issue时，请包含：
- 操作系统和Node.js版本
- 详细的错误信息
- 重现步骤
- 相关的输入文件示例

## 📞 技术支持

- **GitHub Issues**: [项目Issues页面]
- **文档**: 本README文件
- **示例**: `tests/` 目录中的示例文件

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者和用户！

特别感谢：
- **Clash** 项目提供的配置格式标准
- **V2Ray** 项目的协议规范
- **Node.js** 社区的优秀库支持

---

## 📱 快速开始

```bash
# 1. 安装依赖
npm install

# 2. 准备订阅文件
# 将您的订阅文件放入 tests/ 目录

# 3. 启动交互式菜单
npm run menu

# 4. 选择操作并享受！
```

**🎉 享受使用代理节点转换工具！**

> 💡 **提示**: 如果您觉得这个工具有用，请给项目一个⭐Star支持！
