/**
 * 代理节点转换工具使用示例
 */

import { ProxyConverter } from '../src/index.js';
import { OutputFormats } from '../src/types.js';
import { DeduplicationStrategy } from '../src/utils/deduplication.js';

// 示例代理URL
const exampleUrls = [
  'ss://<EMAIL>:8388#香港节点1',
  'ss://<EMAIL>:8388#HK Node 2',
  'vmess://eyJ2IjoiMiIsInBzIjoi5pel5pys6IqC54K5IiwiYWRkIjoianAuZXhhbXBsZS5jb20iLCJwb3J0IjoiODA4MCIsImlkIjoiMTIzNDU2NzgtYWJjZC0xMjM0LWFiY2QtMTIzNDU2Nzg5YWJjIiwiYWlkIjoiMCIsInNjeSI6ImF1dG8iLCJuZXQiOiJ0Y3AiLCJ0eXBlIjoibm9uZSIsImhvc3QiOiIiLCJwYXRoIjoiIiwidGxzIjoiIn0=',
  'vless://<EMAIL>:443?encryption=none&security=tls&type=ws&host=us.example.com&path=/ws#美国节点1',
  'trojan://<EMAIL>:443?security=tls&type=tcp&sni=sg.example.com#Singapore Node',
];

async function basicUsage() {
  console.log('=== 基础使用示例 ===\n');

  const converter = new ProxyConverter();

  // 1. 解析代理URL
  console.log('1. 解析代理URL:');
  const nodes = converter.parse(exampleUrls);
  console.log(`解析到 ${nodes.length} 个节点\n`);

  // 2. 节点去重
  console.log('2. 节点去重:');
  const deduplicatedNodes = converter.deduplicate(nodes, {
    strategy: DeduplicationStrategy.SERVER_PORT_TYPE
  });
  console.log(`去重后剩余 ${deduplicatedNodes.length} 个节点\n`);

  // 3. 节点重命名
  console.log('3. 节点重命名:');
  const renamedNodes = converter.rename(deduplicatedNodes, {
    template: '{flag} {region} {index:3}',
    groupByRegion: true
  });
  renamedNodes.forEach(node => {
    console.log(`  ${node.name}`);
  });
  console.log();

  // 4. 转换为不同格式
  console.log('4. 格式转换:');

  // 转换为Clash配置
  const clashConfig = converter.convert(renamedNodes, OutputFormats.CLASH);
  console.log(`Clash配置: ${clashConfig.proxies.length} 个代理`);

  // 转换为Base64订阅
  const base64Sub = converter.convert(renamedNodes, OutputFormats.BASE64);
  console.log(`Base64订阅: ${base64Sub.length} 字符`);

  // 转换为URL列表
  const urlList = converter.convert(renamedNodes, OutputFormats.URL);
  console.log(`URL列表: ${urlList.split('\n').length} 行\n`);
}

async function advancedUsage() {
  console.log('=== 高级使用示例 ===\n');

  const converter = new ProxyConverter();

  // 一键处理
  console.log('1. 一键处理:');
  const result = converter.process(exampleUrls, OutputFormats.CLASH, {
    deduplicate: true,
    rename: true,
    deduplicateOptions: {
      strategy: DeduplicationStrategy.FULL,
      keepFirst: true
    },
    renameOptions: {
      template: '{flag} {region} {index:3}',
      groupByRegion: true,
      startIndex: 1
    },
    convertOptions: {
      baseConfig: {
        port: 7890,
        'socks-port': 7891,
        'allow-lan': true
      }
    }
  });
  console.log(`一键处理完成，生成 ${result.proxies.length} 个代理\n`);

  // 批量处理
  console.log('2. 批量处理:');
  const batchInputs = [
    {
      content: exampleUrls.slice(0, 3),
      format: OutputFormats.URL
    },
    {
      content: 'c3M6Ly9ZV1Z6TFRJMU5pMW5ZMjA2Y0dGemMzZHZjbVE9QDE5Mi4xNjguMS4xOjgzODgjVGVzdA==',
      format: OutputFormats.BASE64
    }
  ];

  const batchResult = converter.batchProcess(batchInputs, OutputFormats.JSON);
  console.log(`批量处理完成，合并 ${batchResult.length} 个节点\n`);

  // 自定义重命名模板
  console.log('3. 自定义重命名模板:');
  const customRenamed = converter.rename(batchResult, {
    template: '🚀 {region} - {type} - {index:2}',
    groupByRegion: false
  });
  customRenamed.forEach(node => {
    console.log(`  ${node.name}`);
  });
  console.log();
}

async function customProcessing() {
  console.log('=== 自定义处理示例 ===\n');

  const converter = new ProxyConverter();

  // 解析节点
  const nodes = converter.parse(exampleUrls);

  // 自定义去重逻辑
  console.log('1. 自定义去重:');
  const { customDeduplicate } = await import('../src/utils/deduplication.js');
  const customDeduped = customDeduplicate(nodes, (node) => {
    // 基于服务器域名去重
    return node.server.split('.')[0];
  });
  console.log(`自定义去重后: ${customDeduped.length} 个节点\n`);

  // 按地区分组重命名
  console.log('2. 按地区分组重命名:');
  const { batchRename } = await import('../src/utils/rename.js');
  const regionTemplates = {
    'HK': '🇭🇰 港区 {index:2}',
    'JP': '🇯🇵 日区 {index:2}',
    'US': '🇺🇸 美区 {index:2}',
    'SG': '🇸🇬 新区 {index:2}',
    'default': '🌐 其他 {index:2}'
  };

  const batchRenamed = batchRename(customDeduped, regionTemplates);
  batchRenamed.forEach(node => {
    console.log(`  ${node.name} (${node.detectedRegion})`);
  });
  console.log();

  // 生成自定义Clash配置
  console.log('3. 自定义Clash配置:');
  const { createCustomClashConfig } = await import('../src/converters/clash.js');
  const customClashConfig = createCustomClashConfig(batchRenamed, {
    config: {
      port: 8080,
      'socks-port': 8081,
      'allow-lan': true,
      mode: 'global'
    },
    proxyGroups: [
      {
        name: '🚀 代理选择',
        type: 'select',
        proxies: ['♻️ 自动选择', ...batchRenamed.map(n => n.name)]
      },
      {
        name: '♻️ 自动选择',
        type: 'url-test',
        proxies: batchRenamed.map(n => n.name),
        url: 'http://www.gstatic.com/generate_204',
        interval: 300
      }
    ]
  });
  console.log(`自定义Clash配置生成完成\n`);
}

async function statisticsExample() {
  console.log('=== 统计信息示例 ===\n');

  const converter = new ProxyConverter();
  const nodes = converter.parse(exampleUrls);

  // 获取详细统计
  const stats = converter.getStats(nodes);
  console.log('节点统计信息:');
  console.log(`总数: ${stats.total}`);
  console.log(`有效: ${stats.valid}`);
  console.log(`无效: ${stats.invalid}`);
  console.log('\n协议分布:');
  Object.entries(stats.types).forEach(([type, count]) => {
    console.log(`  ${type}: ${count}`);
  });
  console.log('\n地区分布:');
  Object.entries(stats.regions).forEach(([region, count]) => {
    console.log(`  ${region}: ${count}`);
  });

  // 去重统计
  const { getDeduplicationStats } = await import('../src/utils/deduplication.js');
  const deduped = converter.deduplicate(nodes);
  const dedupStats = getDeduplicationStats(nodes, deduped);
  console.log('\n去重统计:');
  console.log(`原始节点: ${dedupStats.original}`);
  console.log(`去重后: ${dedupStats.deduplicated}`);
  console.log(`移除重复: ${dedupStats.removed}`);
  console.log(`重复组数: ${dedupStats.duplicateGroups}`);

  // 重命名统计
  const { getRenameStats } = await import('../src/utils/rename.js');
  const renamed = converter.rename(deduped);
  const renameStats = getRenameStats(deduped, renamed);
  console.log('\n重命名统计:');
  console.log(`总数: ${renameStats.total}`);
  console.log(`地区数: ${renameStats.regionCount}`);
  console.log('各地区节点数:');
  Object.entries(renameStats.regions).forEach(([region, count]) => {
    console.log(`  ${region}: ${count}`);
  });
}

// 运行所有示例
async function runAllExamples() {
  try {
    await basicUsage();
    await advancedUsage();
    await customProcessing();
    await statisticsExample();

    console.log('🎉 所有示例运行完成！');
  } catch (error) {
    console.error('❌ 示例运行出错:', error);
  }
}

// 如果直接运行此文件，则执行示例
if (import.meta.url === `file://${process.argv[1]}`) {
  runAllExamples();
}

export {
  basicUsage,
  advancedUsage,
  customProcessing,
  statisticsExample,
  runAllExamples
};
